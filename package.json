{"name": "confluence-upload", "version": "1.0.0", "description": "CLI tool to upload markdown content to Confluence pages.", "bin": {"confluence-upload": "dist/cli/update-confluence-page-cli.js", "confluence-credentials": "dist/cli/credential-manager-cli.js"}, "engines": {"node": ">=18.14.0 || >=23.0.0"}, "scripts": {"build": "tsc", "clean": "rimraf coverage dist node_modules && if exist coverage rmdir /s /q coverage && if exist dist rmdir /s /q dist && if exist node_modules rmdir /s /q node_modules", "start": "npm run build && node dist/cli/update-confluence-page-cli.js", "credentials": "npm run build && node dist/cli/credential-manager-cli.js", "example": "npm run build && node dist/cli/update-confluence-page-cli.js 3265810487 test.md SBB", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:debug": "cross-env JEST_VERBOSE=true jest --verbose", "test:utils": "jest tests/utils", "test:integration": "jest tests/utils/integration.test.ts", "test:errors": "jest tests/errors"}, "dependencies": {"axios": "^1.7.9", "marked": "^15.0.12", "puppeteer": "^23.10.4"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^22.10.2", "cross-env": "^7.0.3", "jest": "^30.0.4", "nyc": "^17.1.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}