# Design Document

## Overview

This design document outlines the architectural improvements and refactoring approach for the Confluence page update project. The refactoring will focus on creating a cleaner, more maintainable codebase while preserving all existing functionality. The design emphasizes modularity, consistency, and developer experience improvements.

## Architecture

### Current Architecture Issues

The current codebase suffers from several architectural problems:
- Scattered logging and console output throughout modules
- Duplicated password handling and encryption logic
- Inconsistent error handling patterns
- Mixed concerns in single files
- Legacy migration code that's no longer needed
- Test and development files mixed with production code

### Target Architecture

The refactored architecture will follow these principles:
- **Separation of Concerns**: Clear boundaries between CLI, business logic, and utilities
- **Single Responsibility**: Each module has one clear purpose
- **Dependency Injection**: Shared services injected rather than directly instantiated
- **Layered Architecture**: Presentation → Business Logic → Data Access → Utilities

```
┌─────────────────────────────────────────────────────────────┐
│                    CLI Layer                                │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ credential-cli  │  │ update-confluence-page-cli      │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 Business Logic Layer                        │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ CredentialMgr   │  │ ConfluencePageUpdater           │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Service Layer                             │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ AuthService     │  │ CookieService                   │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Utility Layer                             │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ CryptoUtils     │  │ FileUtils     │ LoggingUtils    │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Components and Interfaces

### 1. Logging System

**Purpose**: Centralized, configurable logging with different verbosity levels

**Interface**:
```typescript
interface Logger {
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
  debug(message: string, ...args: any[]): void;
  verbose(message: string, ...args: any[]): void;
}

interface LoggerConfig {
  level: 'silent' | 'error' | 'warn' | 'info' | 'debug' | 'verbose';
  prefix?: string;
}
```

**Implementation Strategy**:
- Replace all `console.log/error/warn` calls with logger methods
- Default to 'info' level for production, 'debug' for development
- Add `--verbose` and `--quiet` CLI flags
- Remove debug messages from normal operation flow

### 2. Crypto Utilities

**Purpose**: Shared encryption/decryption functionality

**Interface**:
```typescript
interface CryptoUtils {
  encryptMasterKey(masterKey: Buffer, password: string): EncryptedMasterKey;
  decryptMasterKey(encrypted: EncryptedMasterKey, password: string): Buffer;
  encryptData(data: string, key: Buffer): EncryptedData;
  decryptData(encrypted: EncryptedData, key: Buffer): string;
  generateKey(): Buffer;
  secureWipe(buffer: Buffer): void;
}
```

**Implementation Strategy**:
- Extract common encryption logic from SecureCredentialsManager and SecureCookieManager
- Use consistent PBKDF2 parameters across all encryption operations
- Implement secure memory wiping for sensitive data

### 3. Password Input Utility

**Purpose**: Consistent, secure password input across all CLI tools

**Interface**:
```typescript
interface PasswordInput {
  askPassword(prompt: string): Promise<string>;
  askPasswordWithConfirmation(prompt: string): Promise<string>;
}
```

**Implementation Strategy**:
- Extract password input logic from credential-manager-cli.ts
- Fix character echoing issues completely
- Support both TTY and non-TTY environments
- Implement proper cleanup on Ctrl+C

### 4. Configuration Management

**Purpose**: Centralized configuration and constants

**Interface**:
```typescript
interface AppConfig {
  paths: {
    configDir: string;
    keyFile: string;
    credentialsFile: string;
    cookiesFile: string;
  };
  crypto: {
    keyLength: number;
    pbkdf2Iterations: number;
    algorithm: string;
  };
  retry: {
    maxAttempts: number;
    timeoutMs: number;
  };
  messages: MessageCatalog;
}
```

### 5. Error Handling System

**Purpose**: Consistent error types and handling patterns

**Interface**:
```typescript
abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly userMessage: string;
  abstract readonly exitCode: number;
}

class AuthenticationError extends AppError { /* ... */ }
class FileOperationError extends AppError { /* ... */ }
class NetworkError extends AppError { /* ... */ }
class ValidationError extends AppError { /* ... */ }
```

### 6. File Management Utilities

**Purpose**: Consistent file operations with proper error handling

**Interface**:
```typescript
interface FileUtils {
  ensureDirectory(path: string, mode?: number): Promise<void>;
  secureWrite(path: string, data: string | Buffer, mode?: number): Promise<void>;
  secureRead(path: string): Promise<Buffer>;
  secureDelete(path: string): Promise<void>;
  exists(path: string): Promise<boolean>;
}
```

## Data Models

### Refined Interfaces

```typescript
// Consolidated credential interface
interface ConfluenceCredential {
  readonly id: string; // Add unique identifier
  name: string;
  baseUrl: string;
  spaceKey: string;
  authMethod: 'token' | 'browser';
  token?: string;
  username?: string;
  password?: string;
  createdAt: number;
  updatedAt: number;
}

// Enhanced cookie interface
interface EnhancedCookie {
  name: string;
  value: string;
  domain: string;
  path?: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
  savedAt: number;
  baseUrl: string;
}

// Configuration interfaces
interface EncryptedStorage {
  version: string;
  salt: string;
  iv: string;
  encryptedData: string;
  algorithm: string;
}
```

## Error Handling

### Error Classification

1. **User Errors**: Invalid input, missing files, wrong credentials
   - Show helpful message with correction guidance
   - Exit code: 1

2. **System Errors**: File permissions, network issues, disk space
   - Show technical details with suggested fixes
   - Exit code: 2

3. **Application Errors**: Bugs, unexpected states, corrupted data
   - Show generic error with support information
   - Exit code: 3

### Error Handling Patterns

```typescript
// Consistent error handling wrapper
async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (error instanceof AppError) {
      logger.error(error.userMessage);
      process.exit(error.exitCode);
    } else {
      logger.error(`Unexpected error in ${context}: ${error.message}`);
      process.exit(3);
    }
  }
}
```

## Testing Strategy

### Test Organization

1. **Unit Tests**: Individual utility functions and classes
2. **Integration Tests**: Component interactions
3. **CLI Tests**: End-to-end command-line interface testing
4. **Security Tests**: Encryption/decryption validation

### Test File Structure

```
tests/
├── unit/
│   ├── crypto-utils.test.ts
│   ├── file-utils.test.ts
│   └── password-input.test.ts
├── integration/
│   ├── credential-manager.test.ts
│   └── cookie-manager.test.ts
└── cli/
    ├── credential-cli.test.ts
    └── update-cli.test.ts
```

## Migration Strategy

### Phase 1: Utility Extraction
1. Create utility modules (crypto, file, password, logging)
2. Extract common functionality from existing classes
3. Update existing classes to use utilities
4. Remove duplicated code

### Phase 2: Interface Standardization
1. Define consistent interfaces for all components
2. Update implementations to match interfaces
3. Add proper TypeScript typing throughout

### Phase 3: Error Handling Improvement
1. Create error class hierarchy
2. Replace generic error handling with typed errors
3. Improve user-facing error messages

### Phase 4: Logging Cleanup
1. Implement centralized logger
2. Replace all console.* calls
3. Add verbosity controls
4. Remove debug messages from production flow

### Phase 5: Dead Code Removal
1. Identify and remove unused files
2. Remove legacy migration code
3. Clean up test and development files
4. Remove unused imports and functions

### Phase 6: CLI Consistency
1. Standardize command-line argument parsing
2. Improve help text and documentation
3. Ensure consistent exit codes and cleanup

## File Organization

### Proposed Structure

```
src/
├── cli/
│   ├── credential-manager-cli.ts
│   └── update-confluence-page-cli.ts
├── services/
│   ├── credential-service.ts
│   ├── cookie-service.ts
│   └── confluence-service.ts
├── utils/
│   ├── crypto-utils.ts
│   ├── file-utils.ts
│   ├── password-input.ts
│   └── logger.ts
├── types/
│   ├── credentials.ts
│   ├── cookies.ts
│   └── errors.ts
├── config/
│   ├── app-config.ts
│   └── messages.ts
└── index.ts
```

### Files to Remove

Based on analysis, these files should be removed:
- `test-*.ts` files (move to tests/ directory or remove if obsolete)
- `quick-cookie-diagnosis.ts` (development utility)
- `cookie-reuse-test.ts` (development utility)
- `simple-cookie-upload.ts` (redundant with main functionality)
- `simplified-update.ts` (redundant with main functionality)
- Legacy migration code within existing files

## Performance Considerations

### Startup Optimization
- Lazy load heavy dependencies (puppeteer)
- Cache configuration and credentials in memory
- Minimize file system operations during startup

### Memory Management
- Clear sensitive data from memory after use
- Implement proper cleanup in error scenarios
- Use streaming for large file operations

### Network Optimization
- Implement connection pooling for API requests
- Add appropriate timeouts and retry logic
- Cache authentication tokens when possible

## Security Enhancements

### Password Handling
- Implement proper password masking without character echoing
- Clear password variables from memory after use
- Add password strength validation for new passwords

### Logging Security
- Redact sensitive information from logs
- Implement log rotation and secure deletion
- Add audit trail for credential operations

### File Security
- Ensure proper file permissions on all created files
- Implement secure temporary file handling
- Add integrity checks for encrypted files

## Backward Compatibility

### Migration Support
- Maintain compatibility with existing credential files
- Provide migration utilities for configuration changes
- Support legacy command-line arguments during transition

### API Stability
- Maintain existing CLI interfaces
- Preserve configuration file formats where possible
- Provide deprecation warnings for removed features